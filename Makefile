GO      ?= go
CLANG   ?= clang
BPF_SRC = tools/schedlat/bpf/schedlat.c
BPF_OBJ = tools/schedlat/bpf/schedlat.o
OUTPUT_DIR = output
OUTPUT_BIN = $(OUTPUT_DIR)/schedlat

# Detect target architecture
GOARCH ?= $(shell $(GO) env GOARCH)
ifeq ($(GOARCH),arm64)
    BPF_TARGET_ARCH = aarch64
else ifeq ($(GOARCH),amd64)
    BPF_TARGET_ARCH = x86_64
else
    BPF_TARGET_ARCH = $(GOARCH)
endif

all: $(OUTPUT_BIN)

$(BPF_OBJ): $(BPF_SRC)
	$(CLANG) -O2 -g -target bpf -D__TARGET_ARCH_$(BPF_TARGET_ARCH) -c $< -o $@

$(OUTPUT_BIN): $(BPF_OBJ) tools/schedlat/main.go go.mod | $(OUTPUT_DIR)
	cd tools/schedlat && GOARCH= $(GO) generate && GOARCH=$(GOARCH) $(GO) build -o ../../$@

$(OUTPUT_DIR):
	mkdir -p $(OUTPUT_DIR)

clean:
	rm -f $(OUTPUT_BIN) $(BPF_OBJ)
	rmdir $(OUTPUT_DIR) 2>/dev/null || true

.PHONY: all clean
