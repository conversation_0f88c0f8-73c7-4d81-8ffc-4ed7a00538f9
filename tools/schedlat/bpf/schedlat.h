#include "../../../common/bpf/types.h"

#define BPF_MAP_TYPE_RINGBUF 27
#define BPF_MAP_TYPE_HASH 1
#define BPF_ANY 0

struct trace_entry {
	unsigned short type;
	unsigned char flags;
	unsigned char preempt_count;
	int pid;
	unsigned char preempt_lazy_count;
};

struct trace_event_raw_sched_wakeup_template {
	struct trace_entry ent;
	char comm[16];
	pid_t pid;
	int prio;
	int target_cpu;
	char __data[0];
};

struct trace_event_raw_sched_switch {
	struct trace_entry ent;
	char prev_comm[16];       
	pid_t prev_pid;          
	int prev_prio;           
	long prev_state;         
	char next_comm[16];      
	pid_t next_pid;          
	int next_prio;           
	char __data[0];
};

struct sched_event {
    u32 pid;
    u32 tgid;
    char comm[16];
    u64 wakeup_time;
    u64 switch_time;
    u64 latency_ns;
};