# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build Commands

- **Install dependencies**: `sudo apt install -y llvm clang libbpf-dev golang-go`
- **Generate BPF bindings**: `cd tools/schedlat && go generate` (must be run before building)
- **Build for x86**: `make` (outputs to `output/schedlat`)
- **Build for aarch64**: `GOARCH=arm64 make`
- **Clean build artifacts**: `make clean`

## Project Structure

```
tools/schedlat/           # Main schedlat tool
├── main.go              # Go userspace program
└── bpf/                 # BPF kernel programs (separate from Go to avoid compilation conflicts)
    ├── schedlat.c       # eBPF kernel program
    ├── schedlat.h       # Type definitions
    └── schedlat.o       # Compiled BPF object
common/bpf/              # Shared BPF type definitions
└── types.h              # Common type definitions used across BPF programs
output/                  # Build output directory
└── schedlat             # Final executable binary
```

## Architecture

This is an eBPF-based scheduler latency monitoring tool built with <PERSON> and the cilium/ebpf library.

### BPF Program (`tools/schedlat/bpf/`)
- **schedlat.c**: eBPF kernel program that hooks into scheduler tracepoints
- **schedlat.h**: Type definitions for tracepoint structures and events
- Monitors `sched_wakeup` and `sched_switch` kernel tracepoints
- Uses BPF maps: ringbuffer for events, hash map for wakeup times, and PID filtering
- Filters out latencies < 10ms and idle processes (PID 0)

### Go Userspace Program (`tools/schedlat/main.go`)
- Uses `//go:generate` directive to auto-generate BPF bindings via bpf2go
- BPF code generation references `bpf/schedlat.c` (relative path)
- Implements PID filtering for process-specific monitoring (-p flag)
- Converts kernel monotonic timestamps to human-readable local time
- Handles thread enumeration for multi-threaded process monitoring
- Real-time event processing from BPF ringbuffer

### Key Components
- **Event Structure**: Matches between BPF and Go (pid, tgid, comm, timestamps, latency)
- **Time Conversion**: Handles CLOCK_MONOTONIC to CLOCK_REALTIME conversion
- **PID Filtering**: Maps process PIDs to all thread PIDs for accurate monitoring
- **Signal Handling**: Graceful shutdown with Ctrl+C

### Dependencies
- `github.com/cilium/ebpf`: BPF program loading and interaction
- `golang.org/x/sys/unix`: Low-level system calls
- Standard Go libraries for binary parsing, time handling, and process management

The tool outputs scheduler latency events in real-time with timestamp, PID, command name, and latency in microseconds.