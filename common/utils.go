package common

import (
	"fmt"
	"os"
	"strconv"
)

// 获取指定进程的所有线程PID
func GetetProcessThreads(pid int) ([]int, error) {
	taskDir := fmt.Sprintf("/proc/%d/task", pid)

	// 检查进程是否存在
	if _, err := os.Stat(taskDir); os.IsNotExist(err) {
		return nil, fmt.Errorf("进程 %d 不存在", pid)
	}

	entries, err := os.ReadDir(taskDir)
	if err != nil {
		return nil, fmt.Errorf("读取 %s 失败: %v", taskDir, err)
	}

	var pids []int
	for _, entry := range entries {
		if entry.IsDir() {
			if tid, err := strconv.Atoi(entry.Name()); err == nil {
				pids = append(pids, tid)
			}
		}
	}

	return pids, nil
}
